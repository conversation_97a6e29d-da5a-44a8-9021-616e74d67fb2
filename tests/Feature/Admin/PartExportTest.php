<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartExportTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Part $part;
    private MobileModel $model;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        // Create test data
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $category = Category::factory()->create(['name' => 'Display']);
        
        $this->part = Part::factory()->create([
            'name' => 'LCD Screen',
            'part_number' => 'LCD-001',
            'description' => 'High quality LCD screen',
            'manufacturer' => 'Samsung',
            'category_id' => $category->id,
        ]);

        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'brand_id' => $brand->id,
        ]);

        // Attach model to part with pivot data
        $this->part->models()->attach($this->model->id, [
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front',
            'front_camera_type' => '12MP',
            'camera_position' => 'Front',
            'battery_mah' => '3095',
            'pin_model' => 'Lightning',
            'connector_types' => 'USB-C',
            'types' => 'Premium',
            'is_verified' => true,
            'compatibility_notes' => 'Fully compatible',
            'additional_info' => 'Tested and verified'
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function export_single_uses_column_configuration()
    {
        // Set up column configuration with only specific columns enabled
        $config = $this->columnService->getColumnConfiguration(true);
        $config['brand']['enabled'] = true;
        $config['model']['enabled'] = true;
        $config['part_name']['enabled'] = true;
        $config['front_camera_type']['enabled'] = true;
        $config['verified']['enabled'] = true;
        
        // Disable other columns
        foreach ($config as $key => $column) {
            if (!in_array($key, ['brand', 'model', 'part_name', 'front_camera_type', 'verified'])) {
                $config[$key]['enabled'] = false;
            }
        }
        
        $this->columnService->updateColumnConfiguration($config);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        $content = $response->getContent();
        $lines = explode("\n", trim($content));

        // Debug: Check what's actually in the CSV
        dump('CSV Content:', $content);
        dump('Lines:', $lines);

        // Check header contains only enabled columns
        $header = str_getcsv($lines[0]);
        dump('Header:', $header);
        $this->assertContains('ID', $header);
        $this->assertContains('Brand', $header);
        $this->assertContains('Model', $header);
        $this->assertContains('Part Name', $header);
        $this->assertContains('Front Camera Type', $header);
        $this->assertContains('Status', $header);
        
        // Check that disabled columns are not present
        $this->assertNotContains('Model Number', $header);
        $this->assertNotContains('Description', $header);
        $this->assertNotContains('Display Type', $header);

        // Check data row
        $dataRow = str_getcsv($lines[1]);
        $this->assertEquals($this->part->id, $dataRow[0]); // ID
        $this->assertEquals('Apple', $dataRow[array_search('Brand', $header)]);
        $this->assertEquals('iPhone 13', $dataRow[array_search('Model', $header)]);
        $this->assertEquals('LCD Screen', $dataRow[array_search('Part Name', $header)]);
        $this->assertEquals('12MP', $dataRow[array_search('Front Camera Type', $header)]);
        $this->assertEquals('true', $dataRow[array_search('Status', $header)]);
    }

    /** @test */
    public function export_single_handles_missing_pivot_data()
    {
        // Create a model without pivot data
        $brand = Brand::factory()->create(['name' => 'Samsung']);
        $model = MobileModel::factory()->create([
            'name' => 'Galaxy S21',
            'brand_id' => $brand->id,
        ]);

        $this->part->models()->attach($model->id, [
            'is_verified' => false,
        ]);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(200);
        
        $content = $response->getContent();
        $lines = explode("\n", trim($content));
        
        // Should have 2 data rows (iPhone 13 and Galaxy S21)
        $this->assertCount(3, $lines); // header + 2 data rows
        
        // Check that missing data shows as empty or default values
        $galaxyRow = str_getcsv($lines[2]);
        $header = str_getcsv($lines[0]);
        
        $this->assertEquals('Samsung', $galaxyRow[array_search('Brand', $header)]);
        $this->assertEquals('Galaxy S21', $galaxyRow[array_search('Model', $header)]);
    }

    /** @test */
    public function download_compatibility_template_uses_column_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        $content = $response->getContent();
        $lines = explode("\n", trim($content));
        
        // Should have header + 3 sample rows
        $this->assertGreaterThanOrEqual(4, count($lines));
        
        $header = str_getcsv($lines[0]);
        $this->assertContains('ID', $header);
        $this->assertContains('Brand', $header);
        $this->assertContains('Model', $header);
    }

    /** @test */
    public function export_requires_enabled_setting()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }
}
